const bcrypt = require('bcrypt');
const ExpressError = require('@build-connect/utils/ExpressError');
const User = require('../../model/user');

// Apply to become a broker
exports.applyAsBroker = async (req, res) => {
    const { 
        name, 
        email, 
        phone, 
        password, 
        location, 
        experience 
    } = req.body;

    // Validate required fields
    if (!name || !email || !phone || !password || !location) {
        throw new ExpressError('All required fields must be provided', 400);
    }

    try {
        // Check if user already exists
        const existingUser = await User.findOne({ 
            $or: [{ email }, { phone }] 
        });

        if (existingUser) {
            throw new ExpressError('User with this email or phone already exists', 409);
        }

        const hashedPw = await bcrypt.hash(password, 10);

        // Handle avatar upload
        let avatarUrl;
        if (req.file && req.file.path) {
            avatarUrl = req.file.path;
        }

        // Handle documents upload (multiple files)
        let documentsUrls = [];
        if (req.files && req.files.documents) {
            documentsUrls = req.files.documents.map(file => file.path);
        }

        const brokerApplication = new User({
            name,
            email,
            phone,
            password: hashedPw,
            avatar: avatarUrl,
            location,
            experience: experience || 0,
            documents: documentsUrls,
            role: 'broker',
            verificationStatus: 'pending',
            isActive: false // Inactive until approved
        });

        await brokerApplication.save();

        res.status(201).json({
            message: 'Broker application submitted successfully. Awaiting admin approval.',
            applicationId: brokerApplication._id
        });

    } catch (error) {
        if (error.code === 11000) {
            if (error.keyPattern.email) {
                throw new ExpressError('Email already exists', 409);
            }
            if (error.keyPattern.phone) {
                throw new ExpressError('Phone number already exists', 409);
            }
        }
        throw new ExpressError(`Failed to submit application: ${error.message}`, 500);
    }
};

// Get broker application status
exports.getApplicationStatus = async (req, res) => {
    const userId = req.user.id;

    try {
        const user = await User.findById(userId).select('-password');
        
        if (!user) {
            throw new ExpressError('User not found', 404);
        }

        if (user.role !== 'broker') {
            throw new ExpressError('User is not a broker applicant', 400);
        }

        res.status(200).json({
            message: 'Application status retrieved successfully',
            application: {
                id: user._id,
                name: user.name,
                email: user.email,
                phone: user.phone,
                location: user.location,
                experience: user.experience,
                documents: user.documents,
                verificationStatus: user.verificationStatus,
                approvalDate: user.approvalDate,
                isActive: user.isActive,
                createdAt: user.createdAt,
                updatedAt: user.updatedAt
            }
        });

    } catch (error) {
        throw new ExpressError(`Failed to get application status: ${error.message}`, 500);
    }
};

// Update broker application (before approval)
exports.updateApplication = async (req, res) => {
    const userId = req.user.id;
    const { name, location, experience } = req.body;

    try {
        const user = await User.findById(userId);
        
        if (!user) {
            throw new ExpressError('User not found', 404);
        }

        if (user.role !== 'broker') {
            throw new ExpressError('User is not a broker applicant', 400);
        }

        if (user.verificationStatus !== 'pending') {
            throw new ExpressError('Cannot update application after it has been processed', 400);
        }

        // Handle avatar upload
        let avatarUrl = user.avatar;
        if (req.file && req.file.path) {
            avatarUrl = req.file.path;
        }

        // Handle documents upload (multiple files)
        let documentsUrls = user.documents;
        if (req.files && req.files.documents) {
            documentsUrls = req.files.documents.map(file => file.path);
        }

        const updateData = {};
        if (name) updateData.name = name;
        if (location) updateData.location = location;
        if (experience !== undefined) updateData.experience = experience;
        if (avatarUrl !== user.avatar) updateData.avatar = avatarUrl;
        if (documentsUrls !== user.documents) updateData.documents = documentsUrls;

        const updatedUser = await User.findByIdAndUpdate(
            userId,
            updateData,
            { new: true, runValidators: true }
        ).select('-password');

        res.status(200).json({
            message: 'Application updated successfully',
            application: updatedUser
        });

    } catch (error) {
        throw new ExpressError(`Failed to update application: ${error.message}`, 500);
    }
};
