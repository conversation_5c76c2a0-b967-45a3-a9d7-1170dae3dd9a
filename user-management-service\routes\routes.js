const express = require('express');

const router = express.Router();
const { catchAsync } = require('@build-connect/utils');
const createAuthMiddleware = require('@build-connect/utils/middleware/authmiddleware');
const multer = require('multer');
const { client } = require('../cache');
const authControllers = require('../controllers/user/auth');
const profileControllers = require('../controllers/user/profile');
const managementControllers = require('../controllers/user/management');

const { Cloudinary } = require('../cloudinary');

const upload = multer({ storage: Cloudinary.getStorage() });

const doAuthenticate = createAuthMiddleware.NewAuthenticateMiddleware(client);

router.get('/', doAuthenticate, (req, res) => {
    const { user } = req;
    res.status(200).json(user);
});

router.post('/login', catchAsync(authControllers.login));

router.post(
    '/signup',
    upload.single('avatar'),
    catchAsync(authControllers.signup)
);

router.post('/refresh', catchAsync(authControllers.refreshToken));

// User profile routes
router.get('/user/profile', doAuthenticate, catchAsync(profileControllers.getUserProfile));
router.put('/user/profile', doAuthenticate, upload.single('avatar'), catchAsync(profileControllers.updateUserProfile));

// User management routes
router.get('/users', doAuthenticate, catchAsync(managementControllers.getUsersByRole));

module.exports = router;
