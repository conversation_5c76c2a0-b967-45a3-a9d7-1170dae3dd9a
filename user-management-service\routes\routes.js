const express = require('express');

const router = express.Router();
const { catchAsync } = require('@build-connect/utils');
const createAuthMiddleware = require('@build-connect/utils/middleware/authmiddleware');
const multer = require('multer');
const { client } = require('../cache');
const authControllers = require('../controllers/user/auth');
const profileControllers = require('../controllers/user/profile');
const managementControllers = require('../controllers/user/management');
const brokerApplicationControllers = require('../controllers/broker/application');
const brokerManagementControllers = require('../controllers/broker/management');
const brokerProfileControllers = require('../controllers/broker/profile');

const { Cloudinary } = require('../cloudinary');

const upload = multer({ storage: Cloudinary.getStorage() });
const uploadMultiple = multer({ storage: Cloudinary.getStorage() });

const doAuthenticate = createAuthMiddleware.NewAuthenticateMiddleware(client);

router.get('/', doAuthenticate, (req, res) => {
    const { user } = req;
    res.status(200).json(user);
});

router.post('/login', catchAsync(authControllers.login));

router.post(
    '/signup',
    upload.single('avatar'),
    catchAsync(authControllers.signup)
);

router.post('/refresh', catchAsync(authControllers.refreshToken));

// User profile routes
router.get('/user/profile', doAuthenticate, catchAsync(profileControllers.getUserProfile));
router.put('/user/profile', doAuthenticate, upload.single('avatar'), catchAsync(profileControllers.updateUserProfile));

// User management routes
router.get('/users', doAuthenticate, catchAsync(managementControllers.getUsersByRole));

// Broker application routes
router.post('/broker/apply',
    uploadMultiple.fields([
        { name: 'avatar', maxCount: 1 },
        { name: 'documents', maxCount: 10 }
    ]),
    catchAsync(brokerApplicationControllers.applyAsBroker)
);

router.get('/broker/application/status', doAuthenticate, catchAsync(brokerApplicationControllers.getApplicationStatus));

router.put('/broker/application/update',
    doAuthenticate,
    uploadMultiple.fields([
        { name: 'avatar', maxCount: 1 },
        { name: 'documents', maxCount: 10 }
    ]),
    catchAsync(brokerApplicationControllers.updateApplication)
);

// Broker profile routes (for verified brokers)
router.get('/broker/profile', doAuthenticate, catchAsync(brokerProfileControllers.getBrokerProfile));

router.put('/broker/profile',
    doAuthenticate,
    uploadMultiple.fields([
        { name: 'avatar', maxCount: 1 },
        { name: 'documents', maxCount: 10 }
    ]),
    catchAsync(brokerProfileControllers.updateBrokerProfile)
);

router.delete('/broker/profile/document', doAuthenticate, catchAsync(brokerProfileControllers.removeDocument));

router.get('/broker/public/:brokerId', catchAsync(brokerProfileControllers.getPublicBrokerProfile));

// Broker management routes (Admin only)
router.get('/admin/broker/applications', doAuthenticate, catchAsync(brokerManagementControllers.getBrokerApplications));

router.get('/admin/broker/applications/:applicationId', doAuthenticate, catchAsync(brokerManagementControllers.getBrokerApplicationById));

router.put('/admin/broker/applications/:applicationId/approve', doAuthenticate, catchAsync(brokerManagementControllers.approveBrokerApplication));

router.put('/admin/broker/applications/:applicationId/reject', doAuthenticate, catchAsync(brokerManagementControllers.rejectBrokerApplication));

router.get('/admin/broker/active', doAuthenticate, catchAsync(brokerManagementControllers.getActiveBrokers));

router.put('/admin/broker/:brokerId/deactivate', doAuthenticate, catchAsync(brokerManagementControllers.deactivateBroker));

module.exports = router;
