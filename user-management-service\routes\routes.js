const express = require('express');

const router = express.Router();
const { catchAsync } = require('@build-connect/utils');
const createAuthMiddleware = require('@build-connect/utils/middleware/authmiddleware');
const multer = require('multer');
const { client } = require('../cache');
const userControllers = require('../controllers/users');

const { Cloudinary } = require('../cloudinary');

const upload = multer({ storage: Cloudinary.getStorage() });

const doAuthenticate = createAuthMiddleware.NewAuthenticateMiddleware(client);

router.get('/', doAuthenticate, (req, res) => {
    const { user } = req;
    res.status(200).json(user);
});

router.post('/login', catchAsync(userControllers.login));

router.post(
    '/signup',
    upload.single('avatar'),
    catchAsync(userControllers.signup)
);

router.post('/refresh', catchAsync(userControllers.refreshToken));

// User profile routes
router.get('/user/profile', doAuthenticate, catchAsync(userControllers.getUserProfile));
router.put('/user/profile', doAuthenticate, upload.single('avatar'), catchAsync(userControllers.updateUserProfile));

// User documents and portfolio routes
router.post('/user/documents', doAuthenticate, upload.array('documents', 10), catchAsync(userControllers.updateUserDocuments));
router.put('/user/portfolio', doAuthenticate, upload.array('portfolio', 10), catchAsync(userControllers.updateUserPortfolio));

// User specialties and experience routes
router.put('/user/specialties', doAuthenticate, catchAsync(userControllers.updateUserSpecialties));
router.put('/user/experience', doAuthenticate, catchAsync(userControllers.updateUserExperience));

// User verification routes
router.put('/user/:userId/verify', doAuthenticate, catchAsync(userControllers.verifyUser));
router.put('/user/verify-email', doAuthenticate, catchAsync(userControllers.verifyEmail));
router.put('/user/verify-phone', doAuthenticate, catchAsync(userControllers.verifyPhone));

// User management routes
router.get('/users', doAuthenticate, catchAsync(userControllers.getUsersByRole));
router.put('/user/deactivate', doAuthenticate, catchAsync(userControllers.deactivateUser));
router.post('/user/:userId/rating', doAuthenticate, catchAsync(userControllers.addUserRating));

module.exports = router;
