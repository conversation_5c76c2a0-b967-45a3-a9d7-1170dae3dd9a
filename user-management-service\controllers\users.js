const bcrypt = require('bcrypt');
const {
    createJSONToken,
    sessionID,
    base64Encode,
    getDecodedToken,
} = require('@build-connect/utils');
const ExpressError = require('@build-connect/utils/ExpressError');
const User = require('../model/user');

const { client } = require('../cache');
const { JWT_REFRESH_KEY, JWT_ACCESS_KEY } = require('../config').getAll();

exports.signup = async (req, res) => {
    const { email, password, phone, name } = req.body;
    const hashedPw = await bcrypt.hash(password, 10);

    let avatarUrl;
    if (req.file && req.file.path) {
        avatarUrl = req.file.path;
    }

    const user = new User({
        email,
        password: hashedPw,
        phone,
        name,
        avatar: avatarUrl,
    });

    try {
        await user.save();
    } catch (error) {
        if (
            error.code === 11000 &&
            error.keyPattern &&
            error.keyPattern.email
        ) {
            throw new ExpressError('user already exists', 409);
        }
        throw error;
    }

    res.status(200).json({
        message: 'Successfully signed up!',
    });
};

exports.login = async (req, res) => {
    const { email, password } = req.body;
    const user = await User.findOne({ email });

    if (!user) {
        throw new ExpressError('email or password is incorrect', 401);
    }

    const isMatch = await bcrypt.compare(password, user.password);
    if (!isMatch) {
        throw new ExpressError('email or password is incorrect', 401);
    }

    const accessToken = createJSONToken({}, JWT_ACCESS_KEY, '10m');

    const refreshToken = createJSONToken(
        { userId: user._id.toString() },
        JWT_REFRESH_KEY,
        '720h'
    );

    const id = sessionID();

    const idToken = {
        id: user._id.toString(),
        email: user.email,
        name: user.name,
        role: user.role,
    };

    const tokenResponse = {
        accessToken,
        refreshToken,
        idToken: base64Encode(JSON.stringify(idToken)),
    };

    await client.set(id, JSON.stringify(tokenResponse), { EX: 720 * 3600 });

    res.status(200).json({
        message: 'logged in sucessfully',
        accessToken,
        sessionID: id,
    });
};

exports.refreshToken = async (req, res) => {
    const id = req.get('Session');

    if (!id) {
        return res.status(401).json({ message: 'unauthorized' });
    }

    const tokenResponse = await client.get(id);

    if (!tokenResponse) {
        return res.status(401).json({ message: 'unauthorized' });
    }

    const parsedTokenResponse = JSON.parse(tokenResponse);
    getDecodedToken(parsedTokenResponse.refreshToken);

    const accessToken = createJSONToken({}, JWT_ACCESS_KEY, '10m');

    const token = {
        ...parsedTokenResponse,
        accessToken,
    };

    await client.set(id, JSON.stringify(token), {
        KEEPTTL: true,
    });

    res.status(200).json({ accessToken });
};

// Get user profile
exports.getUserProfile = async (req, res) => {
    const userId = req.user.id;

    try {
        const user = await User.findById(userId).select('-password');
        if (!user) {
            throw new ExpressError('User not found', 404);
        }

        // Update last login
        user.lastLoginAt = new Date();
        await user.save();

        res.status(200).json({
            message: 'Profile retrieved successfully',
            user
        });
    } catch (error) {
        throw new ExpressError(`Failed to get profile: ${error.message}`, 500);
    }
};

// Update user profile
exports.updateUserProfile = async (req, res) => {
    const userId = req.user.id;
    const { name, location, phone } = req.body;

    let avatarUrl;
    if (req.file && req.file.path) {
        avatarUrl = req.file.path;
    }

    try {
        const updateData = {};
        if (name) updateData.name = name;
        if (location) updateData.location = location;
        if (phone) updateData.phone = phone;
        if (avatarUrl) updateData.avatar = avatarUrl;

        const user = await User.findByIdAndUpdate(
            userId,
            updateData,
            { new: true, runValidators: true }
        ).select('-password');

        if (!user) {
            throw new ExpressError('User not found', 404);
        }

        res.status(200).json({
            message: 'Profile updated successfully',
            user
        });
    } catch (error) {
        if (error.code === 11000) {
            throw new ExpressError('Phone number already exists', 409);
        }
        throw new ExpressError(`Failed to update profile: ${error.message}`, 500);
    }
};

// Get users by role 
exports.getUsersByRole = async (req, res) => {
    const { role } = req.query;
    const currentUser = await User.findById(req.user.id);

    if (!currentUser || currentUser.role !== 'admin') {
        throw new ExpressError('Admin access required', 403);
    }

    try {
        const query = role ? { role } : {};
        const users = await User.find(query).select('-password');

        res.status(200).json({
            message: 'Users retrieved successfully',
            users,
            count: users.length
        });
    } catch (error) {
        throw new ExpressError(`Failed to get users: ${error.message}`, 500);
    }
};
