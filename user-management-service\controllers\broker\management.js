const ExpressError = require('@build-connect/utils/ExpressError');
const User = require('../../model/user');

// Get all broker applications (Admin only)
exports.getBrokerApplications = async (req, res) => {
    const { status } = req.query; // pending, verified, rejected
    const currentUser = await User.findById(req.user.id);

    if (!currentUser || currentUser.role !== 'admin') {
        throw new ExpressError('Admin access required', 403);
    }

    try {
        const query = { role: 'broker' };
        if (status) {
            query.verificationStatus = status;
        }

        const applications = await User.find(query)
            .select('-password')
            .sort({ createdAt: -1 });

        res.status(200).json({
            message: 'Broker applications retrieved successfully',
            applications,
            count: applications.length
        });

    } catch (error) {
        throw new ExpressError(`Failed to get broker applications: ${error.message}`, 500);
    }
};

// Get specific broker application details (Admin only)
exports.getBrokerApplicationById = async (req, res) => {
    const { applicationId } = req.params;
    const currentUser = await User.findById(req.user.id);

    if (!currentUser || currentUser.role !== 'admin') {
        throw new ExpressError('Admin access required', 403);
    }

    try {
        const application = await User.findOne({ 
            _id: applicationId, 
            role: 'broker' 
        }).select('-password');

        if (!application) {
            throw new ExpressError('Broker application not found', 404);
        }

        res.status(200).json({
            message: 'Broker application details retrieved successfully',
            application
        });

    } catch (error) {
        throw new ExpressError(`Failed to get broker application: ${error.message}`, 500);
    }
};

// Approve broker application (Admin only)
exports.approveBrokerApplication = async (req, res) => {
    const { applicationId } = req.params;
    const currentUser = await User.findById(req.user.id);

    if (!currentUser || currentUser.role !== 'admin') {
        throw new ExpressError('Admin access required', 403);
    }

    try {
        const application = await User.findOne({ 
            _id: applicationId, 
            role: 'broker' 
        });

        if (!application) {
            throw new ExpressError('Broker application not found', 404);
        }

        if (application.verificationStatus !== 'pending') {
            throw new ExpressError('Application has already been processed', 400);
        }

        const updatedApplication = await User.findByIdAndUpdate(
            applicationId,
            {
                verificationStatus: 'verified',
                approvalDate: new Date(),
                isActive: true
            },
            { new: true, runValidators: true }
        ).select('-password');

        res.status(200).json({
            message: 'Broker application approved successfully',
            application: updatedApplication
        });

    } catch (error) {
        throw new ExpressError(`Failed to approve broker application: ${error.message}`, 500);
    }
};

// Reject broker application (Admin only)
exports.rejectBrokerApplication = async (req, res) => {
    const { applicationId } = req.params;
    const { rejectionReason } = req.body;
    const currentUser = await User.findById(req.user.id);

    if (!currentUser || currentUser.role !== 'admin') {
        throw new ExpressError('Admin access required', 403);
    }

    try {
        const application = await User.findOne({ 
            _id: applicationId, 
            role: 'broker' 
        });

        if (!application) {
            throw new ExpressError('Broker application not found', 404);
        }

        if (application.verificationStatus !== 'pending') {
            throw new ExpressError('Application has already been processed', 400);
        }

        const updateData = {
            verificationStatus: 'rejected',
            approvalDate: new Date(),
            isActive: false
        };

        // Add rejection reason if provided (you might want to add this field to the schema)
        if (rejectionReason) {
            updateData.rejectionReason = rejectionReason;
        }

        const updatedApplication = await User.findByIdAndUpdate(
            applicationId,
            updateData,
            { new: true, runValidators: true }
        ).select('-password');

        res.status(200).json({
            message: 'Broker application rejected successfully',
            application: updatedApplication
        });

    } catch (error) {
        throw new ExpressError(`Failed to reject broker application: ${error.message}`, 500);
    }
};

// Get all active brokers (Admin only)
exports.getActiveBrokers = async (req, res) => {
    const currentUser = await User.findById(req.user.id);

    if (!currentUser || currentUser.role !== 'admin') {
        throw new ExpressError('Admin access required', 403);
    }

    try {
        const brokers = await User.find({ 
            role: 'broker', 
            verificationStatus: 'verified',
            isActive: true 
        })
        .select('-password')
        .sort({ approvalDate: -1 });

        res.status(200).json({
            message: 'Active brokers retrieved successfully',
            brokers,
            count: brokers.length
        });

    } catch (error) {
        throw new ExpressError(`Failed to get active brokers: ${error.message}`, 500);
    }
};

// Deactivate broker (Admin only)
exports.deactivateBroker = async (req, res) => {
    const { brokerId } = req.params;
    const currentUser = await User.findById(req.user.id);

    if (!currentUser || currentUser.role !== 'admin') {
        throw new ExpressError('Admin access required', 403);
    }

    try {
        const broker = await User.findOne({ 
            _id: brokerId, 
            role: 'broker',
            verificationStatus: 'verified'
        });

        if (!broker) {
            throw new ExpressError('Verified broker not found', 404);
        }

        const updatedBroker = await User.findByIdAndUpdate(
            brokerId,
            { isActive: false },
            { new: true, runValidators: true }
        ).select('-password');

        res.status(200).json({
            message: 'Broker deactivated successfully',
            broker: updatedBroker
        });

    } catch (error) {
        throw new ExpressError(`Failed to deactivate broker: ${error.message}`, 500);
    }
};
