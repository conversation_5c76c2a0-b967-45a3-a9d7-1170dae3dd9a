const ExpressError = require('@build-connect/utils/ExpressError');
const User = require('../../model/user');

// Get users by role 
exports.getUsersByRole = async (req, res) => {
    const { role } = req.query;
    const currentUser = await User.findById(req.user.id);

    if (!currentUser || currentUser.role !== 'admin') {
        throw new ExpressError('Admin access required', 403);
    }

    try {
        const query = role ? { role } : {};
        const users = await User.find(query).select('-password');

        res.status(200).json({
            message: 'Users retrieved successfully',
            users,
            count: users.length
        });
    } catch (error) {
        throw new ExpressError(`Failed to get users: ${error.message}`, 500);
    }
};
