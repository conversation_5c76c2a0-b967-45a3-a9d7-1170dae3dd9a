const bcrypt = require('bcrypt');
const {
    createJSONToken,
    sessionID,
    base64Encode,
    getDecodedToken,
} = require('@build-connect/utils');
const ExpressError = require('@build-connect/utils/ExpressError');
const User = require('../../model/user');

const { client } = require('../../cache');
const { JWT_REFRESH_KEY, JWT_ACCESS_KEY } = require('../../config').getAll();

exports.signup = async (req, res) => {
    const { email, password, phone, name } = req.body;
    const hashedPw = await bcrypt.hash(password, 10);

    let avatarUrl;
    if (req.file && req.file.path) {
        avatarUrl = req.file.path;
    }

    const user = new User({
        email,
        password: hashedPw,
        phone,
        name,
        avatar: avatarUrl,
    });

    try {
        await user.save();
    } catch (error) {
        if (
            error.code === 11000 &&
            error.keyPattern &&
            error.keyPattern.email
        ) {
            throw new ExpressError('user already exists', 409);
        }
        throw error;
    }

    res.status(200).json({
        message: 'Successfully signed up!',
    });
};

exports.login = async (req, res) => {
    const { email, password } = req.body;
    const user = await User.findOne({ email });

    if (!user) {
        throw new ExpressError('email or password is incorrect', 401);
    }

    const isMatch = await bcrypt.compare(password, user.password);
    if (!isMatch) {
        throw new ExpressError('email or password is incorrect', 401);
    }

    const accessToken = createJSONToken({}, JWT_ACCESS_KEY, '10m');

    const refreshToken = createJSONToken(
        { userId: user._id.toString() },
        JWT_REFRESH_KEY,
        '720h'
    );

    const id = sessionID();

    const idToken = {
        id: user._id.toString(),
        email: user.email,
        name: user.name,
        role: user.role,
    };

    const tokenResponse = {
        accessToken,
        refreshToken,
        idToken: base64Encode(JSON.stringify(idToken)),
    };

    await client.set(id, JSON.stringify(tokenResponse), { EX: 720 * 3600 });

    res.status(200).json({
        message: 'logged in sucessfully',
        accessToken,
        sessionID: id,
    });
};

exports.refreshToken = async (req, res) => {
    const id = req.get('Session');

    if (!id) {
        return res.status(401).json({ message: 'unauthorized' });
    }

    const tokenResponse = await client.get(id);

    if (!tokenResponse) {
        return res.status(401).json({ message: 'unauthorized' });
    }

    const parsedTokenResponse = JSON.parse(tokenResponse);
    getDecodedToken(parsedTokenResponse.refreshToken);

    const accessToken = createJSONToken({}, JWT_ACCESS_KEY, '10m');

    const token = {
        ...parsedTokenResponse,
        accessToken,
    };

    await client.set(id, JSON.stringify(token), {
        KEEPTTL: true,
    });

    res.status(200).json({ accessToken });
};
