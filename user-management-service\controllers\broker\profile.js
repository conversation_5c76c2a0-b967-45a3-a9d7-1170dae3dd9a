const ExpressError = require('@build-connect/utils/ExpressError');
const User = require('../../model/user');

// Get broker profile
exports.getBrokerProfile = async (req, res) => {
    const userId = req.user.id;

    try {
        const broker = await User.findById(userId).select('-password');
        
        if (!broker) {
            throw new ExpressError('Broker not found', 404);
        }

        if (broker.role !== 'broker') {
            throw new ExpressError('User is not a broker', 400);
        }

        // Update last login
        broker.lastLoginAt = new Date();
        await broker.save();

        res.status(200).json({
            message: 'Broker profile retrieved successfully',
            broker: {
                id: broker._id,
                name: broker.name,
                email: broker.email,
                phone: broker.phone,
                avatar: broker.avatar,
                location: broker.location,
                experience: broker.experience,
                documents: broker.documents,
                verificationStatus: broker.verificationStatus,
                approvalDate: broker.approvalDate,
                isActive: broker.isActive,
                ratings: broker.ratings,
                isEmailVerified: broker.isEmailVerified,
                isPhoneVerified: broker.isPhoneVerified,
                lastLoginAt: broker.lastLoginAt,
                createdAt: broker.createdAt,
                updatedAt: broker.updatedAt
            }
        });

    } catch (error) {
        throw new ExpressError(`Failed to get broker profile: ${error.message}`, 500);
    }
};

// Update broker profile (only for verified brokers)
exports.updateBrokerProfile = async (req, res) => {
    const userId = req.user.id;
    const { name, location, experience } = req.body;

    try {
        const broker = await User.findById(userId);
        
        if (!broker) {
            throw new ExpressError('Broker not found', 404);
        }

        if (broker.role !== 'broker') {
            throw new ExpressError('User is not a broker', 400);
        }

        if (broker.verificationStatus !== 'verified') {
            throw new ExpressError('Only verified brokers can update their profile', 403);
        }

        // Handle avatar upload
        let avatarUrl = broker.avatar;
        if (req.file && req.file.path) {
            avatarUrl = req.file.path;
        }

        // Handle documents upload (multiple files) - only add new documents
        let documentsUrls = broker.documents;
        if (req.files && req.files.documents) {
            const newDocuments = req.files.documents.map(file => file.path);
            documentsUrls = [...documentsUrls, ...newDocuments];
        }

        const updateData = {};
        if (name) updateData.name = name;
        if (location) updateData.location = location;
        if (experience !== undefined) updateData.experience = experience;
        if (avatarUrl !== broker.avatar) updateData.avatar = avatarUrl;
        if (documentsUrls !== broker.documents) updateData.documents = documentsUrls;

        const updatedBroker = await User.findByIdAndUpdate(
            userId,
            updateData,
            { new: true, runValidators: true }
        ).select('-password');

        res.status(200).json({
            message: 'Broker profile updated successfully',
            broker: updatedBroker
        });

    } catch (error) {
        if (error.code === 11000) {
            throw new ExpressError('Phone number already exists', 409);
        }
        throw new ExpressError(`Failed to update broker profile: ${error.message}`, 500);
    }
};

// Remove document from broker profile
exports.removeDocument = async (req, res) => {
    const userId = req.user.id;
    const { documentUrl } = req.body;

    if (!documentUrl) {
        throw new ExpressError('Document URL is required', 400);
    }

    try {
        const broker = await User.findById(userId);
        
        if (!broker) {
            throw new ExpressError('Broker not found', 404);
        }

        if (broker.role !== 'broker') {
            throw new ExpressError('User is not a broker', 400);
        }

        if (broker.verificationStatus !== 'verified') {
            throw new ExpressError('Only verified brokers can modify their documents', 403);
        }

        // Remove the document from the array
        const updatedDocuments = broker.documents.filter(doc => doc !== documentUrl);

        const updatedBroker = await User.findByIdAndUpdate(
            userId,
            { documents: updatedDocuments },
            { new: true, runValidators: true }
        ).select('-password');

        res.status(200).json({
            message: 'Document removed successfully',
            broker: updatedBroker
        });

    } catch (error) {
        throw new ExpressError(`Failed to remove document: ${error.message}`, 500);
    }
};

// Get public broker profile (for other users to view)
exports.getPublicBrokerProfile = async (req, res) => {
    const { brokerId } = req.params;

    try {
        const broker = await User.findOne({ 
            _id: brokerId, 
            role: 'broker',
            verificationStatus: 'verified',
            isActive: true
        }).select('-password -documents -phone -email');

        if (!broker) {
            throw new ExpressError('Active verified broker not found', 404);
        }

        // Calculate average rating
        const averageRating = broker.ratings.length > 0 
            ? broker.ratings.reduce((sum, rating) => sum + rating, 0) / broker.ratings.length 
            : 0;

        res.status(200).json({
            message: 'Public broker profile retrieved successfully',
            broker: {
                id: broker._id,
                name: broker.name,
                avatar: broker.avatar,
                location: broker.location,
                experience: broker.experience,
                averageRating: averageRating.toFixed(1),
                totalRatings: broker.ratings.length,
                approvalDate: broker.approvalDate,
                createdAt: broker.createdAt
            }
        });

    } catch (error) {
        throw new ExpressError(`Failed to get public broker profile: ${error.message}`, 500);
    }
};
