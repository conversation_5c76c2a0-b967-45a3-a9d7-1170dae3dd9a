const ExpressError = require('@build-connect/utils/ExpressError');
const User = require('../../model/user');

// Get user profile
exports.getUserProfile = async (req, res) => {
    const userId = req.user.id;

    try {
        const user = await User.findById(userId).select('-password');
        if (!user) {
            throw new ExpressError('User not found', 404);
        }

        // Update last login
        user.lastLoginAt = new Date();
        await user.save();

        res.status(200).json({
            message: 'Profile retrieved successfully',
            user
        });
    } catch (error) {
        throw new ExpressError(`Failed to get profile: ${error.message}`, 500);
    }
};

// Update user profile
exports.updateUserProfile = async (req, res) => {
    const userId = req.user.id;
    const { name, location, phone } = req.body;

    let avatarUrl;
    if (req.file && req.file.path) {
        avatarUrl = req.file.path;
    }

    try {
        const updateData = {};
        if (name) updateData.name = name;
        if (location) updateData.location = location;
        if (phone) updateData.phone = phone;
        if (avatarUrl) updateData.avatar = avatarUrl;

        const user = await User.findByIdAndUpdate(
            userId,
            updateData,
            { new: true, runValidators: true }
        ).select('-password');

        if (!user) {
            throw new ExpressError('User not found', 404);
        }

        res.status(200).json({
            message: 'Profile updated successfully',
            user
        });
    } catch (error) {
        if (error.code === 11000) {
            throw new ExpressError('Phone number already exists', 409);
        }
        throw new ExpressError(`Failed to update profile: ${error.message}`, 500);
    }
};
